import React, { useState } from 'react'
import { Send, Mic, Zap, Loader2 } from 'lucide-react'
import { useAppStore } from '../store/appStore'
import { useAI } from '../hooks/useAI'
import { useVoice } from '../hooks/useVoice'
import ToolsDropdown from './ToolsDropdown'

const ChatInput: React.FC = () => {
  const [input, setInput] = useState('')
  const [showTools, setShowTools] = useState(false)

  const {
    currentConversationId,
    createNewConversation,
    addMessage
  } = useAppStore()

  const { sendMessage, isLoading } = useAI()
  const { toggleVoiceMode, isSupported: voiceSupported } = useVoice()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    const messageContent = input.trim()
    setInput('')

    // Create new conversation if none exists
    let conversationId = currentConversationId
    if (!conversationId) {
      createNewConversation()
      // Get the new conversation ID from the store
      conversationId = useAppStore.getState().currentConversationId!
    }

    // Add user message
    addMessage(conversationId, {
      role: 'user',
      content: messageContent,
    })

    // Send to AI
    await sendMessage(messageContent)
  }

  const handleVoiceMode = () => {
    if (voiceSupported) {
      toggleVoiceMode()
    }
  }

  return (
    <div className="p-4">
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex items-end space-x-3">
          {/* Tools Button */}
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowTools(!showTools)}
              className="p-3 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Zap className="w-5 h-5 text-gray-600" />
            </button>
            
            {showTools && (
              <ToolsDropdown onClose={() => setShowTools(false)} />
            )}
          </div>

          {/* Input Field */}
          <div className="flex-1 relative">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask anything"
              className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={1}
              style={{ minHeight: '48px', maxHeight: '120px' }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  handleSubmit(e)
                }
              }}
              aria-label="Message input"
              aria-describedby="message-help"
            />
            
            {/* Voice Button */}
            <button
              type="button"
              onClick={handleVoiceMode}
              disabled={!voiceSupported}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title={voiceSupported ? "Start voice mode" : "Voice not supported"}
            >
              <Mic className="w-4 h-4 text-gray-500" />
            </button>
          </div>

          {/* Send Button */}
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label={isLoading ? "Sending message..." : "Send message"}
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>

          {/* Hidden help text for screen readers */}
          <div id="message-help" className="sr-only">
            Press Enter to send, Shift+Enter for new line
          </div>
        </div>
      </form>
    </div>
  )
}

export default ChatInput
