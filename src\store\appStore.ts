import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  reasoning?: string
  isThinking?: boolean
}

export interface Conversation {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

export interface Tool {
  id: string
  name: string
  icon: string
  active: boolean
}

interface AppState {
  // UI State
  sidebarCollapsed: boolean
  searchOpen: boolean
  libraryOpen: boolean
  voiceModeActive: boolean
  currentView: 'chat' | 'library' | 'search'
  
  // Conversation State
  conversations: Conversation[]
  currentConversationId: string | null
  
  // Tool State
  activeTools: Tool[]
  
  // AI Model State
  currentModel: 'gemini-2.5-flash' | 'gemini-2.5-pro'
  isThinking: boolean
  
  // Actions
  setSidebarCollapsed: (collapsed: boolean) => void
  setSearchOpen: (open: boolean) => void
  setLibraryOpen: (open: boolean) => void
  setVoiceModeActive: (active: boolean) => void
  setCurrentView: (view: 'chat' | 'library' | 'search') => void
  
  createNewConversation: () => void
  setCurrentConversation: (id: string) => void
  addMessage: (conversationId: string, message: Omit<Message, 'id' | 'timestamp'>) => void
  updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => void
  deleteConversation: (conversationId: string) => void
  clearAllConversations: () => void
  
  toggleTool: (toolId: string) => void
  setCurrentModel: (model: 'gemini-2.5-flash' | 'gemini-2.5-pro') => void
  setIsThinking: (thinking: boolean) => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial UI State
      sidebarCollapsed: false,
      searchOpen: false,
      libraryOpen: false,
      voiceModeActive: false,
      currentView: 'chat',
      
      // Initial Conversation State
      conversations: [],
      currentConversationId: null,
      
      // Initial Tool State
      activeTools: [
        { id: 'create-image', name: 'Create Image', icon: 'image', active: false },
        { id: 'think-longer', name: 'Think Longer', icon: 'brain', active: false },
        { id: 'deep-research', name: 'Deep Research', icon: 'search', active: false },
        { id: 'web-search', name: 'Web Search', icon: 'globe', active: false },
        { id: 'spaces', name: 'Spaces', icon: 'layout', active: false },
        { id: 'create-video', name: 'Create Video', icon: 'video', active: false },
      ],
      
      // Initial AI Model State
      currentModel: 'gemini-2.5-flash',
      isThinking: false,
      
      // UI Actions
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setSearchOpen: (open) => set({ searchOpen: open }),
      setLibraryOpen: (open) => set({ libraryOpen: open }),
      setVoiceModeActive: (active) => set({ voiceModeActive: active }),
      setCurrentView: (view) => set({ currentView: view }),
      
      // Conversation Actions
      createNewConversation: () => {
        const newConversation: Conversation = {
          id: crypto.randomUUID(),
          title: 'New Chat',
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        }
        set((state) => ({
          conversations: [newConversation, ...state.conversations],
          currentConversationId: newConversation.id,
        }))
      },
      
      setCurrentConversation: (id) => set({ currentConversationId: id }),
      
      addMessage: (conversationId, message) => {
        const newMessage: Message = {
          ...message,
          id: crypto.randomUUID(),
          timestamp: new Date(),
        }
        
        set((state) => ({
          conversations: state.conversations.map((conv) =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messages: [...conv.messages, newMessage],
                  updatedAt: new Date(),
                  title: conv.messages.length === 0 ? message.content.slice(0, 50) + '...' : conv.title,
                }
              : conv
          ),
        }))
      },
      
      updateMessage: (conversationId, messageId, updates) => {
        set((state) => ({
          conversations: state.conversations.map((conv) =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messages: conv.messages.map((msg) =>
                    msg.id === messageId ? { ...msg, ...updates } : msg
                  ),
                  updatedAt: new Date(),
                }
              : conv
          ),
        }))
      },

      // Conversation Deletion
      deleteConversation: (conversationId) => {
        set((state) => {
          const updatedConversations = state.conversations.filter(conv => conv.id !== conversationId)
          const newCurrentId = state.currentConversationId === conversationId
            ? (updatedConversations.length > 0 ? updatedConversations[0].id : null)
            : state.currentConversationId

          return {
            conversations: updatedConversations,
            currentConversationId: newCurrentId,
          }
        })
      },

      clearAllConversations: () => {
        set({
          conversations: [],
          currentConversationId: null,
        })
      },

      // Tool Actions
      toggleTool: (toolId) => {
        set((state) => ({
          activeTools: state.activeTools.map((tool) =>
            tool.id === toolId ? { ...tool, active: !tool.active } : tool
          ),
          currentModel: toolId === 'think-longer' && !state.activeTools.find(t => t.id === toolId)?.active 
            ? 'gemini-2.5-pro' 
            : state.currentModel,
        }))
      },
      
      setCurrentModel: (model) => set({ currentModel: model }),
      setIsThinking: (thinking) => set({ isThinking: thinking }),
    }),
    {
      name: 'kana-app-storage',
      partialize: (state) => ({
        conversations: state.conversations,
        currentConversationId: state.currentConversationId,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
    }
  )
)
