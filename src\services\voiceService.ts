export class VoiceService {
  private recognition: SpeechRecognition | null = null
  private synthesis: SpeechSynthesis
  private isListening = false
  private isSpeaking = false

  constructor() {
    this.synthesis = window.speechSynthesis

    // Initialize speech recognition if available
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new (window as any).webkitSpeechRecognition()
    } else if ('SpeechRecognition' in window) {
      this.recognition = new (window as any).SpeechRecognition()
    }

    if (this.recognition) {
      this.recognition.continuous = true
      this.recognition.interimResults = true
      this.recognition.lang = 'en-US'
    }
  }

  isSupported(): boolean {
    return !!(this.recognition && this.synthesis)
  }

  async startListening(
    onResult: (transcript: string, isFinal: boolean) => void,
    onError?: (error: string) => void
  ): Promise<void> {
    if (!this.recognition) {
      onError?.('Speech recognition not supported')
      return
    }

    if (this.isListening) {
      return
    }

    this.isListening = true

    this.recognition.onresult = (event) => {
      let transcript = ''
      let isFinal = false

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i]
        transcript += result[0].transcript

        if (result.isFinal) {
          isFinal = true
        }
      }

      onResult(transcript, isFinal)
    }

    this.recognition.onerror = (event) => {
      this.isListening = false
      onError?.(event.error)
    }

    this.recognition.onend = () => {
      this.isListening = false
    }

    try {
      this.recognition.start()
    } catch (error) {
      this.isListening = false
      onError?.('Failed to start speech recognition')
    }
  }

  stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
      this.isListening = false
    }
  }

  async speak(text: string, options: {
    rate?: number
    pitch?: number
    volume?: number
    voice?: SpeechSynthesisVoice
  } = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isSpeaking) {
        this.synthesis.cancel()
      }

      const utterance = new SpeechSynthesisUtterance(text)
      
      utterance.rate = options.rate || 1
      utterance.pitch = options.pitch || 1
      utterance.volume = options.volume || 1
      
      if (options.voice) {
        utterance.voice = options.voice
      }

      utterance.onstart = () => {
        this.isSpeaking = true
      }

      utterance.onend = () => {
        this.isSpeaking = false
        resolve()
      }

      utterance.onerror = (event) => {
        this.isSpeaking = false
        reject(new Error(`Speech synthesis error: ${event.error}`))
      }

      this.synthesis.speak(utterance)
    })
  }

  stopSpeaking(): void {
    if (this.isSpeaking) {
      this.synthesis.cancel()
      this.isSpeaking = false
    }
  }

  getVoices(): SpeechSynthesisVoice[] {
    return this.synthesis.getVoices()
  }

  getIsListening(): boolean {
    return this.isListening
  }

  getIsSpeaking(): boolean {
    return this.isSpeaking
  }
}

// Export a default instance
export const voiceService = new VoiceService()
