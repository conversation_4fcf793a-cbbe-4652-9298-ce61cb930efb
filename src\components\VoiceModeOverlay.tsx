import React from 'react'
import { X, Mic, Info, MicOff } from 'lucide-react'
import { useVoice } from '../hooks/useVoice'

const VoiceModeOverlay: React.FC = () => {
  const {
    isListening,
    isSpeaking,
    transcript,
    error,
    isSupported,
    toggleListening,
    toggleVoiceMode
  } = useVoice()

  const handleClose = () => {
    toggleVoiceMode()
  }

  return (
    <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 z-50">
      <div className="h-full flex flex-col items-center justify-center relative">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-lg transition-colors"
        >
          <X className="w-6 h-6 text-gray-600" />
        </button>

        {/* Voice Orb */}
        <div className="relative mb-8">
          <div className={`w-32 h-32 rounded-full voice-orb flex items-center justify-center transition-all duration-300 ${
            isListening
              ? 'bg-gradient-to-br from-green-400 to-blue-600'
              : isSpeaking
                ? 'bg-gradient-to-br from-purple-400 to-pink-600'
                : 'bg-gradient-to-br from-blue-400 to-purple-600'
          }`}>
            <div className={`w-24 h-24 rounded-full flex items-center justify-center transition-all duration-300 ${
              isListening
                ? 'bg-gradient-to-br from-green-300 to-blue-500'
                : isSpeaking
                  ? 'bg-gradient-to-br from-purple-300 to-pink-500'
                  : 'bg-gradient-to-br from-blue-300 to-purple-500'
            }`}>
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                {isListening ? (
                  <div className="w-8 h-8 bg-red-500 rounded-full animate-pulse"></div>
                ) : (
                  <Mic className="w-8 h-8 text-white" />
                )}
              </div>
            </div>
          </div>

          {/* Pulse rings - only show when active */}
          {(isListening || isSpeaking) && (
            <>
              <div className="absolute inset-0 rounded-full border-2 border-blue-400/30 animate-ping"></div>
              <div className="absolute inset-2 rounded-full border-2 border-purple-400/30 animate-ping animation-delay-75"></div>
            </>
          )}
        </div>

        {/* Status Text */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Voice Mode</h2>
          {!isSupported ? (
            <p className="text-red-600">Voice recognition not supported in this browser</p>
          ) : error ? (
            <p className="text-red-600">{error}</p>
          ) : isListening ? (
            <p className="text-green-600">Listening... Speak now</p>
          ) : isSpeaking ? (
            <p className="text-purple-600">Speaking...</p>
          ) : (
            <p className="text-gray-600">Tap the microphone to start speaking</p>
          )}

          {/* Live transcript */}
          {transcript && (
            <div className="mt-4 p-3 bg-white/60 rounded-lg backdrop-blur-sm border">
              <p className="text-gray-800 text-sm">{transcript}</p>
            </div>
          )}
        </div>

        {/* Control Buttons */}
        <div className="flex items-center space-x-8">
          <button
            onClick={toggleListening}
            disabled={!isSupported}
            className={`p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ${
              isListening
                ? 'bg-red-500 text-white'
                : 'bg-white text-blue-600 hover:bg-blue-50'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isListening ? (
              <MicOff className="w-6 h-6" />
            ) : (
              <Mic className="w-6 h-6" />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default VoiceModeOverlay
