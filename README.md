# Kana - AI Chatbot Interface

**Know More, Be More**

A modern, feature-rich ChatGPT-like AI chatbot interface built with React, TypeScript, and Google Gemini AI.

## Features

- 🤖 **AI Chat Interface** - Clean, intuitive chat experience with streaming responses
- 🧠 **Enhanced Reasoning** - "Think Longer" mode with gemini-2.5-pro for complex queries
- 🎤 **Voice Mode** - Speech-to-text and text-to-speech capabilities
- 🔧 **Tools System** - Extensible tools framework (Create Image, Deep Research, Web Search, etc.)
- 📚 **Library** - Manage generated content and images
- 🔍 **Search** - Search through conversation history
- 💾 **Persistent Storage** - Conversations saved locally with Zustand
- 🎨 **Modern UI** - Beautiful design with Tailwind CSS and smooth animations
- ♿ **Accessibility** - Screen reader support and keyboard navigation
- 📱 **Responsive** - Works on desktop and mobile devices

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Google Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kana-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Google Gemini API key:
   ```
   VITE_GOOGLE_API_KEY=your_google_api_key_here
   ```
   
   Get your API key from: https://aistudio.google.com/app/apikey

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:5173` (or the port shown in terminal)

## Usage

### Basic Chat
- Type your message in the input field
- Press Enter to send (Shift+Enter for new line)
- AI responses stream in real-time

### Voice Mode
- Click the microphone icon to enter voice mode
- Speak naturally - the app will transcribe and respond with voice
- Works in supported browsers (Chrome, Edge, Safari)

### Enhanced Reasoning
- Click the tools button (⚡) and enable "Think Longer"
- The AI will use gemini-2.5-pro and show its reasoning process
- Great for complex problems and detailed analysis

### Tools System
Available tools include:
- **Think Longer** - Enhanced reasoning with gemini-2.5-pro
- **Create Image** - Generate images (placeholder)
- **Deep Research** - In-depth research capabilities (placeholder)
- **Web Search** - Search the web (placeholder)
- **Spaces** - Collaborative workspaces (placeholder)
- **Create Video** - Video generation (placeholder)

## Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Lucide React icons
- **AI**: Google Gemini API (gemini-2.5-flash, gemini-2.5-pro)
- **State Management**: Zustand with persistence
- **Voice**: Web Speech API (SpeechRecognition, SpeechSynthesis)
- **Animations**: Framer Motion, CSS animations
- **Build Tool**: Vite with TypeScript

## Project Structure

```
src/
├── components/          # React components
│   ├── ChatInterface.tsx
│   ├── Sidebar.tsx
│   ├── VoiceModeOverlay.tsx
│   └── ...
├── hooks/              # Custom React hooks
│   ├── useAI.ts
│   ├── useVoice.ts
│   └── ...
├── services/           # API and external services
│   ├── geminiService.ts
│   ├── voiceService.ts
│   └── ...
├── store/              # State management
│   └── appStore.ts
└── types/              # TypeScript type definitions
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Check the GitHub issues
- Review the documentation
- Ensure your API key is properly configured
