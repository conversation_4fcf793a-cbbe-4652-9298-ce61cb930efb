import { useState, useCallback } from 'react'
import { useAppStore } from '../store/appStore'
import { geminiService, StreamingResponse } from '../services/geminiService'

export const useAI = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  
  const {
    currentConversationId,
    conversations,
    addMessage,
    updateMessage,
    activeTools,
    currentModel,
    setCurrentModel,
    setIsThinking,
  } = useAppStore()

  const sendMessage = useCallback(async (prompt: string) => {
    if (!currentConversationId) {
      console.error('No active conversation')
      return
    }

    setIsLoading(true)
    setIsStreaming(true)

    try {
      // Check if Think Longer tool is active
      const thinkLongerActive = activeTools.find(tool => tool.id === 'think-longer')?.active
      
      // Set model based on tools
      if (thinkLongerActive) {
        setCurrentModel('gemini-2.5-pro')
        setIsThinking(true)
      } else {
        setCurrentModel('gemini-2.5-flash')
        setIsThinking(false)
      }

      // Get conversation history for context
      const conversation = conversations.find(conv => conv.id === currentConversationId)
      if (conversation) {
        // Convert conversation history to Gemini format
        const history = conversation.messages
          .filter(msg => msg.role !== 'assistant' || !msg.isThinking)
          .map(msg => ({
            role: msg.role === 'user' ? 'user' as const : 'model' as const,
            parts: [{ text: msg.content }],
          }))
        
        geminiService.setChatHistory(history)
      }

      // Set the model
      geminiService.setModel(thinkLongerActive ? 'gemini-2.5-pro' : 'gemini-2.5-flash')

      // Create initial AI message
      addMessage(currentConversationId, {
        role: 'assistant',
        content: '',
        reasoning: thinkLongerActive ? 'Thinking...' : undefined,
        isThinking: thinkLongerActive,
      })

      // Get the message ID from the updated conversation
      const updatedConversation = useAppStore.getState().conversations.find(conv => conv.id === currentConversationId)
      const aiMessageId = updatedConversation?.messages[updatedConversation.messages.length - 1]?.id

      // Stream the response
      if (aiMessageId) {
        let fullText = ''
        let reasoning = ''

        for await (const chunk of geminiService.streamResponse(prompt, {
          thinkLonger: thinkLongerActive,
        })) {
          fullText = chunk.text
          reasoning = chunk.reasoning || ''

          // Update the message with streaming content
          updateMessage(currentConversationId, aiMessageId, {
            content: fullText,
            reasoning: reasoning || undefined,
            isThinking: !chunk.isComplete && thinkLongerActive,
          })

          if (chunk.isComplete) {
            setIsThinking(false)
            break
          }
        }
      }

    } catch (error) {
      console.error('Error sending message:', error)

      let errorMessage = 'Sorry, I encountered an error while processing your request. Please try again.'

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorMessage = 'API key is missing or invalid. Please set VITE_GOOGLE_API_KEY in your environment.'
        } else if (error.message.includes('quota')) {
          errorMessage = 'API quota exceeded. Please try again later or check your billing.'
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your internet connection and try again.'
        } else if (error.message.includes('rate limit')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.'
        }
      }

      // Add error message
      addMessage(currentConversationId, {
        role: 'assistant',
        content: errorMessage,
      })
    } finally {
      setIsLoading(false)
      setIsStreaming(false)
      setIsThinking(false)
    }
  }, [
    currentConversationId,
    conversations,
    addMessage,
    updateMessage,
    activeTools,
    setCurrentModel,
    setIsThinking,
  ])

  const regenerateResponse = useCallback(async (messageId: string) => {
    if (!currentConversationId) return

    const conversation = conversations.find(conv => conv.id === currentConversationId)
    if (!conversation) return

    const messageIndex = conversation.messages.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // Find the user message that prompted this response
    const userMessage = conversation.messages[messageIndex - 1]
    if (!userMessage || userMessage.role !== 'user') return

    // Remove the AI message and regenerate
    const updatedMessages = conversation.messages.slice(0, messageIndex)
    
    // Update conversation with truncated messages
    // Note: This would require a new store action to update entire message array
    
    // Regenerate response
    await sendMessage(userMessage.content)
  }, [currentConversationId, conversations, sendMessage])

  return {
    sendMessage,
    regenerateResponse,
    isLoading,
    isStreaming,
  }
}
