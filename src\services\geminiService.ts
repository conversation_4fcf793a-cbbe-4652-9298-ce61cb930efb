import { GoogleGenerativeAI } from '@google/generative-ai'

const API_KEY = import.meta.env.VITE_GOOGLE_API_KEY

if (!API_KEY) {
  console.warn('Google API key not found. Please set VITE_GOOGLE_API_KEY in your .env file.')
}

const genAI = new GoogleGenerativeAI(API_KEY || 'demo-key')

export interface StreamingResponse {
  text: string
  isComplete: boolean
  reasoning?: string
}

export interface ChatMessage {
  role: 'user' | 'model'
  parts: { text: string }[]
}

export class GeminiService {
  private model: 'gemini-2.5-flash' | 'gemini-2.5-pro'
  private chatHistory: ChatMessage[] = []

  constructor(model: 'gemini-2.5-flash' | 'gemini-2.5-pro' = 'gemini-2.5-flash') {
    this.model = model
  }

  setModel(model: 'gemini-2.5-flash' | 'gemini-2.5-pro') {
    this.model = model
  }

  setChatHistory(history: ChatMessage[]) {
    this.chatHistory = history
  }

  async *streamResponse(
    prompt: string,
    options: {
      thinkLonger?: boolean
      systemPrompt?: string
    } = {}
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    try {
      // Use gemini-2.5-pro for enhanced reasoning
      const modelName = options.thinkLonger ? 'gemini-2.5-pro' : this.model
      const model = genAI.getGenerativeModel({ model: modelName })

      // Prepare the prompt with thinking instructions if needed
      let finalPrompt = prompt
      if (options.thinkLonger) {
        finalPrompt = `<think>
Let me think about this step by step:
${prompt}

I need to provide a thoughtful and comprehensive response.
</think>

${prompt}`
      }

      // Add system prompt if provided
      if (options.systemPrompt) {
        finalPrompt = `${options.systemPrompt}\n\n${finalPrompt}`
      }

      // Create chat session with history
      const chat = model.startChat({
        history: this.chatHistory,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
      })

      // Send message and stream response
      const result = await chat.sendMessageStream(finalPrompt)

      let fullText = ''
      let reasoning = ''
      let inThinkingBlock = false

      for await (const chunk of result.stream) {
        const chunkText = chunk.text()
        fullText += chunkText

        // Extract reasoning from <think> blocks
        if (options.thinkLonger) {
          const thinkMatch = fullText.match(/<think>(.*?)<\/think>/s)
          if (thinkMatch) {
            reasoning = thinkMatch[1].trim()
            // Remove the thinking block from the displayed text
            fullText = fullText.replace(/<think>.*?<\/think>/s, '').trim()
          }
        }

        yield {
          text: fullText,
          isComplete: false,
          reasoning: reasoning || undefined,
        }
      }

      // Final response
      yield {
        text: fullText,
        isComplete: true,
        reasoning: reasoning || undefined,
      }

      // Update chat history
      this.chatHistory.push(
        { role: 'user', parts: [{ text: prompt }] },
        { role: 'model', parts: [{ text: fullText }] }
      )

    } catch (error) {
      console.error('Error streaming response:', error)

      let errorMessage = 'Sorry, I encountered an error while processing your request. Please try again.'

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorMessage = 'API key is missing or invalid. Please check your configuration.'
        } else if (error.message.includes('quota')) {
          errorMessage = 'API quota exceeded. Please try again later.'
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.'
        } else if (error.message.includes('rate limit')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.'
        }
      }

      yield {
        text: errorMessage,
        isComplete: true,
      }
    }
  }

  async generateResponse(
    prompt: string,
    options: {
      thinkLonger?: boolean
      systemPrompt?: string
    } = {}
  ): Promise<{ text: string; reasoning?: string }> {
    try {
      const modelName = options.thinkLonger ? 'gemini-2.5-pro' : this.model
      const model = genAI.getGenerativeModel({ model: modelName })

      let finalPrompt = prompt
      if (options.thinkLonger) {
        finalPrompt = `<think>
Let me think about this step by step:
${prompt}

I need to provide a thoughtful and comprehensive response.
</think>

${prompt}`
      }

      if (options.systemPrompt) {
        finalPrompt = `${options.systemPrompt}\n\n${finalPrompt}`
      }

      const chat = model.startChat({
        history: this.chatHistory,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        },
      })

      const result = await chat.sendMessage(finalPrompt)
      let text = result.response.text()
      let reasoning: string | undefined

      // Extract reasoning from <think> blocks
      if (options.thinkLonger) {
        const thinkMatch = text.match(/<think>(.*?)<\/think>/s)
        if (thinkMatch) {
          reasoning = thinkMatch[1].trim()
          text = text.replace(/<think>.*?<\/think>/s, '').trim()
        }
      }

      // Update chat history
      this.chatHistory.push(
        { role: 'user', parts: [{ text: prompt }] },
        { role: 'model', parts: [{ text }] }
      )

      return { text, reasoning }
    } catch (error) {
      console.error('Error generating response:', error)

      let errorMessage = 'Sorry, I encountered an error while processing your request. Please try again.'

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorMessage = 'API key is missing or invalid. Please check your configuration.'
        } else if (error.message.includes('quota')) {
          errorMessage = 'API quota exceeded. Please try again later.'
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.'
        } else if (error.message.includes('rate limit')) {
          errorMessage = 'Too many requests. Please wait a moment and try again.'
        }
      }

      return {
        text: errorMessage,
      }
    }
  }

  clearHistory() {
    this.chatHistory = []
  }

  getHistory(): ChatMessage[] {
    return [...this.chatHistory]
  }
}

// Export a default instance
export const geminiService = new GeminiService()
