import { useState, useCallback, useEffect } from 'react'
import { voiceService } from '../services/voiceService'
import { useAppStore } from '../store/appStore'
import { useAI } from './useAI'

export const useVoice = () => {
  const [isListening, setIsListening] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isSupported, setIsSupported] = useState(false)

  const { 
    voiceModeActive, 
    setVoiceModeActive,
    currentConversationId,
    createNewConversation,
    addMessage 
  } = useAppStore()
  
  const { sendMessage } = useAI()

  useEffect(() => {
    setIsSupported(voiceService.isSupported())
  }, [])

  const startListening = useCallback(async () => {
    if (!isSupported) {
      setError('Voice recognition not supported in this browser')
      return
    }

    setError(null)
    setTranscript('')

    try {
      await voiceService.startListening(
        (transcript, isFinal) => {
          setTranscript(transcript)
          
          if (isFinal && transcript.trim()) {
            handleVoiceInput(transcript.trim())
          }
        },
        (error) => {
          setError(error)
          setIsListening(false)
        }
      )
      setIsListening(true)
    } catch (err) {
      setError('Failed to start voice recognition')
      setIsListening(false)
    }
  }, [isSupported])

  const stopListening = useCallback(() => {
    voiceService.stopListening()
    setIsListening(false)
  }, [])

  const handleVoiceInput = useCallback(async (text: string) => {
    // Create new conversation if none exists
    let conversationId = currentConversationId
    if (!conversationId) {
      createNewConversation()
      conversationId = useAppStore.getState().currentConversationId!
    }

    // Add user message
    addMessage(conversationId, {
      role: 'user',
      content: text,
    })

    // Send to AI and get response
    await sendMessage(text)

    // The AI response will be automatically spoken when it's complete
  }, [currentConversationId, createNewConversation, addMessage, sendMessage])

  const speak = useCallback(async (text: string) => {
    if (!isSupported) {
      return
    }

    try {
      setIsSpeaking(true)
      await voiceService.speak(text, {
        rate: 1.0,
        pitch: 1.0,
        volume: 0.8,
      })
    } catch (err) {
      setError('Failed to speak text')
    } finally {
      setIsSpeaking(false)
    }
  }, [isSupported])

  const stopSpeaking = useCallback(() => {
    voiceService.stopSpeaking()
    setIsSpeaking(false)
  }, [])

  const toggleVoiceMode = useCallback(() => {
    if (voiceModeActive) {
      stopListening()
      stopSpeaking()
      setVoiceModeActive(false)
    } else {
      setVoiceModeActive(true)
    }
  }, [voiceModeActive, stopListening, stopSpeaking, setVoiceModeActive])

  const toggleListening = useCallback(() => {
    if (isListening) {
      stopListening()
    } else {
      startListening()
    }
  }, [isListening, startListening, stopListening])

  // Auto-speak AI responses in voice mode
  useEffect(() => {
    if (!voiceModeActive || !currentConversationId) return

    const conversation = useAppStore.getState().conversations.find(
      conv => conv.id === currentConversationId
    )
    
    if (!conversation) return

    const lastMessage = conversation.messages[conversation.messages.length - 1]
    
    // Speak the last AI message if it's complete and has content
    if (
      lastMessage &&
      lastMessage.role === 'assistant' &&
      lastMessage.content &&
      !lastMessage.isThinking
    ) {
      speak(lastMessage.content)
    }
  }, [voiceModeActive, currentConversationId, speak])

  return {
    isListening,
    isSpeaking,
    transcript,
    error,
    isSupported,
    voiceModeActive,
    startListening,
    stopListening,
    speak,
    stopSpeaking,
    toggleVoiceMode,
    toggleListening,
  }
}
